import { forwardRef, useCallback, useImperativeHandle, useRef } from 'react'
import { yupResolver } from '@hookform/resolvers/yup'
import { invoiceFormSchema } from '@ninebot/core/src/validations'
import { Radio } from 'antd'
import { Controller, useForm } from 'react-hook-form'

import { CustomInput } from '@/components/common'

import { Clear } from '../_icons'

interface InvoiceSectionProps {
  selectedInvoice: string
  setSelectedInvoice: (value: string) => void
}
interface FormData {
  type: string
  email: string
  title: string
  tax_id?: string
}

interface InvoiceSectionFormRef {
  validate: () => Promise<{
    email: string
    title: string
    tax_id?: string
  } | null>
  scrollTo: () => Promise<void>
}
const InvoiceSection = forwardRef<InvoiceSectionFormRef, InvoiceSectionProps>((props, ref) => {
  const { selectedInvoice, setSelectedInvoice } = props
  const rootRef = useRef<HTMLDivElement>(null)
  const invoiceOptions = [
    {
      key: '不需要发票',
      value: '0',
    },
    {
      key: '个人数电普票',
      value: '1',
    },
    // {
    //   key: '企业数电普票',
    //   value: '2',
    // },
    // {
    //   key: '企业数电专票',
    //   value: '3',
    // },
  ]

  /**
   * 初始化表单
   */
  const {
    control,
    trigger,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    mode: 'onBlur',
    resolver: yupResolver(invoiceFormSchema),
    defaultValues: {
      type: selectedInvoice,
      email: '',
      title: '',
      tax_id: '',
    },
  })

  /**
   * 自定义输入框失焦验证
   */
  const onBlurHandler = useCallback(
    async (name: keyof FormData) => {
      clearErrors()
      await trigger(name)
    },
    [clearErrors, trigger],
  )

  /**
   * 生成提交数据
   */
  const generateSubmitData = useCallback(
    (formData: FormData) => {
      const data = {
        email: formData.email,
        title: formData.title,
        tax_id: formData.tax_id,
        type: selectedInvoice,
      }
      if (selectedInvoice === '1') {
        delete data.tax_id
      }
      return data
    },
    [selectedInvoice],
  )

  /**
   * 暴露 Api
   */
  useImperativeHandle(ref, () => {
    return {
      /**
       * 验证表单
       */
      validate: async () => {
        if (selectedInvoice === '0') {
          return null
        } else {
          const isValid = await trigger()
          if (isValid) {
            return generateSubmitData(getValues())
          } else {
            return null
          }
        }
      },
      /**
       * 滚动到表单位置
       */
      scrollTo: async () => {
        if (rootRef.current) {
          const rect = rootRef.current.getBoundingClientRect()
          const scrollTop = document.documentElement.scrollTop
          window.scrollTo({
            top: rect.top + scrollTop - 80,
            behavior: 'smooth',
          })
        }
      },
    }
  })

  return (
    <div className="mb-6" ref={rootRef}>
      <h2 className="mb-base-24 text-3xl">发票信息</h2>
      <div className="w-full">
        <Controller
          control={control}
          name="type"
          render={({ field: { onChange } }) => (
            <Radio.Group
              block
              className="custom-radio-group"
              optionType="button"
              value={selectedInvoice}
              onChange={(e) => {
                setSelectedInvoice(e.target.value)
                onChange(e.target.value)
              }}>
              {invoiceOptions.map((option) => (
                <Radio key={option.value} value={option.value}>
                  <div className="flex items-center justify-center leading-[19px]">
                    {option.key}
                  </div>
                </Radio>
              ))}
            </Radio.Group>
          )}
        />
      </div>

      {selectedInvoice !== '0' && (
        <div className="mt-[16px] flex flex-col gap-base-16">
          <div className="w-full">
            <Controller
              control={control}
              name="title"
              render={({ field: { onChange, value } }) => (
                <CustomInput
                  label="发票抬头"
                  labelClassName="text-[#00000066]"
                  leftIcon={<div className="h-[12px] w-[1px] bg-primary" />}
                  rightIcon={<Clear />}
                  onRightIconClick={() => {
                    onChange('')
                    trigger('title')
                  }}
                  required
                  maxLength={100}
                  placeholder="请输入发票抬头"
                  onChange={(e) => {
                    let value = e.target.value
                    if (value.length > 100) {
                      value = value.slice(0, 100)
                    }
                    onChange(value)
                  }}
                  onBlur={() => onBlurHandler('title')}
                  value={value || ''}
                  error={!!errors.title}
                  errorMessage={errors.title?.message as string}
                />
              )}
            />
          </div>

          {selectedInvoice !== '1' && (
            <div className={`w-full`}>
              <Controller
                control={control}
                name="tax_id"
                render={({ field: { onChange, value } }) => (
                  <CustomInput
                    label="企业税号"
                    labelClassName="text-[#00000066]"
                    leftIcon={<div className="h-[12px] w-[1px] bg-primary" />}
                    rightIcon={<Clear />}
                    onRightIconClick={() => {
                      onChange('')
                      trigger('tax_id')
                    }}
                    required
                    maxLength={60}
                    placeholder="请输入企业税号"
                    onChange={(e) => {
                      let value = e.target.value
                      if (value.length > 60) {
                        value = value.slice(0, 60)
                      }
                      onChange(value)
                    }}
                    onBlur={() => onBlurHandler('tax_id')}
                    value={value || ''}
                    error={!!errors.tax_id}
                    errorMessage={errors.tax_id?.message as string}
                  />
                )}
              />
            </div>
          )}

          <div className="w-full">
            <Controller
              control={control}
              name="email"
              render={({ field: { onChange, value } }) => (
                <CustomInput
                  label="接收邮箱"
                  labelClassName="text-[#00000066]"
                  leftIcon={<div className="h-[12px] w-[1px] bg-primary" />}
                  rightIcon={<Clear />}
                  onRightIconClick={() => {
                    onChange('')
                    trigger('email')
                  }}
                  onChange={onChange}
                  onBlur={() => onBlurHandler('email')}
                  required
                  maxLength={60}
                  placeholder="请输入接收邮箱"
                  value={value || ''}
                  error={!!errors.email}
                  errorMessage={errors.email?.message as string}
                />
              )}
            />
          </div>
        </div>
      )}
    </div>
  )
})

InvoiceSection.displayName = 'InvoiceSection'
export default InvoiceSection
