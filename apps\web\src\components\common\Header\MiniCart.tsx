'use client'

import { useMemo } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  NCoinRange,
  NCoinView,
  PriceRange,
  PriceRanges,
  TCartProductItem,
  TRACK_EVENT,
  useVolcAnalytics,
} from '@ninebot/core'
import { useCartProduct } from '@ninebot/core/src/businessHooks'
import { selectCartAllProducts } from '@ninebot/core/src/store'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'

import type { ExtensionInfo } from '@/types/cart'

interface MiniCartProps {
  setIsOpen: (isOpen: boolean) => void
  totalCount: number
  className?: string
  style?: React.CSSProperties
}

const MiniCartItem = ({ item }: { item: TCartProductItem }) => {
  const getI18nString = useTranslations('Common')

  const { product, extension_info, quantity } = item
  const {
    isStatus,
    isStock,
    customAttributes = {},
    isDisplayNCoin = false,
    isPureNCoin = false,
  } = useCartProduct(product as TCartProductItem['product'], extension_info as ExtensionInfo)

  /**
   * 产品是否可用
   */
  const isAvailable = useMemo(() => {
    return isStatus && isStock
  }, [isStatus, isStock])

  return (
    <div
      key={item.uid}
      className="mx-base-24 flex items-stretch justify-between gap-4 border-b border-[#E1E1E4] py-base-24">
      <div className="flex gap-4">
        {/* 商品图片 */}
        <div className="relative h-base-64 w-base-64 overflow-hidden rounded-lg">
          <Image
            src={product?.image?.url ?? '/images/placeholder.png'}
            alt={product?.image?.label ?? 'product image'}
            fill
            className="object-cover"
            priority
          />
          {!isStatus && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#00000080] text-sm text-white">
              已下架
            </div>
          )}
          {isStatus && !isStock && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#00000080] text-sm text-white">
              已售罄
            </div>
          )}
        </div>
        {/* 商品信息 */}
        <div className="flex max-w-[180px] flex-col gap-base">
          <h3 className="line-clamp-1">{product.name}</h3>

          {isAvailable && (
            <>
              {isPureNCoin ? (
                <NCoinRange
                  priceRange={product.price_range as PriceRanges}
                  iconStyle={{ size: 12 }}
                  textStyle="text-base"
                  originIconStyle={{ size: 12 }}
                  originTextStyle="text-base"
                />
              ) : (
                <div className="flex flex-wrap items-center gap-2">
                  <PriceRange
                    priceRange={product.price_range as PriceRanges}
                    direction="row"
                    textStyle="text-base font-miSansDemiBold450"
                  />
                  {isDisplayNCoin && (
                    <NCoinView
                      number={Number(customAttributes.max_usage_limit_ncoins)}
                      prefixText={getI18nString('product_more_use')}
                      iconStyle={{ size: 12 }}
                      textStyle="text-sm"
                    />
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      <div className="flex flex-col items-center justify-center">
        <div className="text-font-primary">x{quantity}</div>
      </div>
    </div>
  )
}

const MiniCart = ({ setIsOpen, totalCount }: MiniCartProps) => {
  const cartItems = useAppSelector(selectCartAllProducts)
  // console.log('cartItems', cartItems)
  const router = useRouter()
  const { reportEvent } = useVolcAnalytics()

  // 过滤出在售商品
  const productItems = useMemo(() => {
    return cartItems?.map((item) => item.rawProduct)
  }, [cartItems])

  // 处理去购物车结算
  const handleGoToCart = () => {
    reportEvent(TRACK_EVENT.shop_homepage_cart_button_click, {
      button_id: 'shop_my_cart',
    })
    // 先关闭 MiniCart
    setIsOpen(false)
    // 然后再跳转
    router.push('/checkout/cart')
  }

  return (
    <div className="w-[404px] rounded-[20px] bg-white p-base-12 text-base shadow-[0px_0px_32px_0px_rgba(0,0,0,0.1)]">
      <div className="flex h-full flex-col">
        {/* 商品列表 */}
        <div className="max-h-[60vh] flex-1 overflow-y-auto">
          {productItems?.map((item) => <MiniCartItem key={item.uid} item={item} />)}
        </div>

        {/* 去购物车结算按钮 */}
        <div className="flex items-center justify-between bg-white p-base-24">
          <span className="font-miSansDemiBold450">共{totalCount}件商品</span>
          <Button type="primary" onClick={handleGoToCart}>
            去购物车结算
          </Button>
        </div>
      </div>
    </div>
  )
}

MiniCart.displayName = 'MiniCart'

export default MiniCart
